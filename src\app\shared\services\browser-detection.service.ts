import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class BrowserDetectionService {

  constructor() { }

  /**
   * Detects if the current browser is Internet Explorer (IE 6-11)
   * Uses multiple detection methods for maximum reliability
   * @returns true if IE is detected, false otherwise
   */
  isInternetExplorer(): boolean {
    // IE 11 detection (most reliable method)
    const isIE11 = !!window.MSInputMethodContext && !!document.documentMode;

    // IE 6-10 detection using conditional compilation
    const isOldIE = /*@cc_on!@*/false || !!document.documentMode;

    // Alternative IE 11 detection (backup method)
    const isIE11Alt = navigator.userAgent.indexOf('Trident/') > -1 && !!document.documentMode;

    return isIE11 || isOldIE || isIE11Alt;
  }

  /**
   * Specifically detects Internet Explorer 11
   * @returns true if IE11 is detected, false otherwise
   */
  isInternetExplorer11(): boolean {
    return !!window.MSInputMethodContext && !!document.documentMode;
  }

  /**
   * Gets the Internet Explorer version if running in IE
   * @returns IE version number or null if not IE
   */
  getIEVersion(): number | null {
    if (document.documentMode) {
      return document.documentMode;
    }
    return null;
  }

  /**
   * Gets the current browser name
   * @returns string representing the browser name
   */
  getBrowserName(): string {
    const userAgent = navigator.userAgent;
    
    if (this.isInternetExplorer()) {
      return 'Internet Explorer';
    } else if (userAgent.indexOf('Edge') > -1) {
      return 'Microsoft Edge';
    } else if (userAgent.indexOf('Chrome') > -1) {
      return 'Google Chrome';
    } else if (userAgent.indexOf('Firefox') > -1) {
      return 'Mozilla Firefox';
    } else if (userAgent.indexOf('Safari') > -1) {
      return 'Safari';
    } else {
      return 'Unknown Browser';
    }
  }

  /**
   * Redirects to Microsoft Edge with the current URL
   * @param showConfirmation whether to show confirmation dialog
   */
  redirectToEdge(showConfirmation: boolean = true): void {
    const currentUrl = window.location.href;
    const edgeUrl = `microsoft-edge:${currentUrl}`;
    
    if (showConfirmation) {
      const userConfirm = confirm(
        'Internet Explorer is not supported for optimal performance.\n\n' +
        'Would you like to open this application in Microsoft Edge for the best experience?\n\n' +
        'Click OK to open in Edge, or Cancel to continue with Internet Explorer.'
      );
      
      if (!userConfirm) {
        console.warn('User chose to continue with Internet Explorer. Some features may not work optimally.');
        return;
      }
    }

    try {
      // Try to open in Edge using the microsoft-edge protocol
      window.location.href = edgeUrl;
      
      // Fallback: If Edge protocol doesn't work, show instructions
      setTimeout(() => {
        alert(
          'Please copy this URL and paste it into Microsoft Edge:\n\n' + currentUrl +
          '\n\nFor the best experience, we recommend using Microsoft Edge or another modern browser.'
        );
      }, 1000);
    } catch (error) {
      // If redirect fails, show manual instructions
      alert(
        'Please copy this URL and paste it into Microsoft Edge:\n\n' + currentUrl +
        '\n\nFor the best experience, we recommend using Microsoft Edge or another modern browser.'
      );
    }
  }

  /**
   * Shows a warning message for unsupported browsers
   */
  showBrowserWarning(): void {
    const browserName = this.getBrowserName();
    console.warn(`Browser detected: ${browserName}. Some features may not work optimally.`);
    
    if (this.isInternetExplorer()) {
      const warningDiv = document.createElement('div');
      warningDiv.innerHTML = `
        <div style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          background-color: #f39c12;
          color: white;
          text-align: center;
          padding: 10px;
          z-index: 9999;
          font-family: Arial, sans-serif;
          font-size: 14px;
        ">
          <strong>Browser Compatibility Notice:</strong> 
          For the best experience, please use Microsoft Edge or another modern browser.
          <button onclick="this.parentElement.parentElement.remove()" style="
            margin-left: 10px;
            background: white;
            color: #f39c12;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 3px;
          ">×</button>
        </div>
      `;
      document.body.appendChild(warningDiv);
    }
  }
}
