<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IE11 Detection Test - DeTrim</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .true { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .false { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .undefined { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        .browser-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            word-break: break-all;
        }
        .method {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .recommended {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        .deprecated {
            border-left-color: #dc3545;
            background: #fff8f8;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Internet Explorer 11 Detection Test</h1>
        
        <div class="browser-info">
            <h3>Current Browser Information:</h3>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
            <p><strong>Document Mode:</strong> <span id="documentMode"></span></p>
            <p><strong>Compatibility Mode:</strong> <span id="compatMode"></span></p>
        </div>

        <h2>🧪 Detection Methods Test Results</h2>

        <div class="method recommended">
            <h3>✅ Method 1: MSInputMethodContext + DocumentMode (RECOMMENDED)</h3>
            <p><strong>Code:</strong> <span class="code">!!window.MSInputMethodContext && !!document.documentMode</span></p>
            <p><strong>Result:</strong> <span id="method1" class="test-result"></span></p>
            <p><strong>Why it works:</strong> MSInputMethodContext is IE11-specific, documentMode confirms it's IE</p>
        </div>

        <div class="method recommended">
            <h3>✅ Method 2: Trident + DocumentMode (RECOMMENDED)</h3>
            <p><strong>Code:</strong> <span class="code">navigator.userAgent.indexOf('Trident/') > -1 && !!document.documentMode</span></p>
            <p><strong>Result:</strong> <span id="method2" class="test-result"></span></p>
            <p><strong>Why it works:</strong> Trident is IE's engine, documentMode confirms it's IE (not Edge Legacy)</p>
        </div>

        <div class="method">
            <h3>⚠️ Method 3: DocumentMode Only</h3>
            <p><strong>Code:</strong> <span class="code">!!document.documentMode</span></p>
            <p><strong>Result:</strong> <span id="method3" class="test-result"></span></p>
            <p><strong>Note:</strong> Works but less specific - could match other IE versions</p>
        </div>

        <div class="method deprecated">
            <h3>❌ Method 4: MSIE in User Agent (FAILS for IE11)</h3>
            <p><strong>Code:</strong> <span class="code">navigator.userAgent.indexOf('MSIE') > -1</span></p>
            <p><strong>Result:</strong> <span id="method4" class="test-result"></span></p>
            <p><strong>Why it fails:</strong> IE11 removed "MSIE" from user agent string</p>
        </div>

        <div class="method deprecated">
            <h3>❌ Method 5: ActiveXObject (FAILS for IE11)</h3>
            <p><strong>Code:</strong> <span class="code">!!window.ActiveXObject</span></p>
            <p><strong>Result:</strong> <span id="method5" class="test-result"></span></p>
            <p><strong>Why it fails:</strong> IE11 removed window.ActiveXObject in standards mode</p>
        </div>

        <div class="method">
            <h3>🔧 Method 6: Conditional Compilation (IE 6-10 only)</h3>
            <p><strong>Code:</strong> <span class="code">/*@cc_on!@*/false</span></p>
            <p><strong>Result:</strong> <span id="method6" class="test-result"></span></p>
            <p><strong>Note:</strong> Only works for IE 6-10, not IE11</p>
        </div>

        <h2>📋 Browser Compatibility Matrix</h2>
        <table>
            <thead>
                <tr>
                    <th>Browser</th>
                    <th>MSInputMethodContext</th>
                    <th>document.documentMode</th>
                    <th>Trident in UA</th>
                    <th>MSIE in UA</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>IE 11</td>
                    <td>✅ true</td>
                    <td>✅ 11</td>
                    <td>✅ true</td>
                    <td>❌ false</td>
                </tr>
                <tr>
                    <td>IE 10</td>
                    <td>❌ undefined</td>
                    <td>✅ 10</td>
                    <td>✅ true</td>
                    <td>✅ true</td>
                </tr>
                <tr>
                    <td>IE 9</td>
                    <td>❌ undefined</td>
                    <td>✅ 9</td>
                    <td>✅ true</td>
                    <td>✅ true</td>
                </tr>
                <tr>
                    <td>Edge Legacy</td>
                    <td>❌ undefined</td>
                    <td>❌ undefined</td>
                    <td>❌ false</td>
                    <td>❌ false</td>
                </tr>
                <tr>
                    <td>Chrome</td>
                    <td>❌ undefined</td>
                    <td>❌ undefined</td>
                    <td>❌ false</td>
                    <td>❌ false</td>
                </tr>
            </tbody>
        </table>

        <h2>🎯 Recommended Implementation</h2>
        <div class="method recommended">
            <h3>Complete IE Detection Function</h3>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;"><code>function isInternetExplorer() {
    // IE 11 detection (most reliable)
    var isIE11 = !!window.MSInputMethodContext && !!document.documentMode;
    
    // IE 6-10 detection
    var isOldIE = /*@cc_on!@*/false || !!document.documentMode;
    
    // Alternative IE 11 detection (backup)
    var isIE11Alt = navigator.userAgent.indexOf('Trident/') > -1 && !!document.documentMode;
    
    return isIE11 || isOldIE || isIE11Alt;
}

function getIEVersion() {
    if (document.documentMode) {
        return document.documentMode;
    }
    return null;
}</code></pre>
        </div>

        <div id="currentResults">
            <h3>🔍 Your Current Browser Results:</h3>
            <p><strong>Is Internet Explorer:</strong> <span id="finalResult"></span></p>
            <p><strong>IE Version:</strong> <span id="ieVersion"></span></p>
        </div>
    </div>

    <script>
        // Detection methods
        function method1_MSInputMethodContext() {
            return !!window.MSInputMethodContext && !!document.documentMode;
        }

        function method2_TridentDocumentMode() {
            return navigator.userAgent.indexOf('Trident/') > -1 && !!document.documentMode;
        }

        function method3_DocumentModeOnly() {
            return !!document.documentMode;
        }

        function method4_MSIEUserAgent() {
            return navigator.userAgent.indexOf('MSIE') > -1;
        }

        function method5_ActiveXObject() {
            return !!window.ActiveXObject;
        }

        function method6_ConditionalCompilation() {
            return /*@cc_on!@*/false;
        }

        // Recommended complete function
        function isInternetExplorer() {
            var isIE11 = !!window.MSInputMethodContext && !!document.documentMode;
            var isOldIE = /*@cc_on!@*/false || !!document.documentMode;
            var isIE11Alt = navigator.userAgent.indexOf('Trident/') > -1 && !!document.documentMode;
            return isIE11 || isOldIE || isIE11Alt;
        }

        function getIEVersion() {
            if (document.documentMode) {
                return document.documentMode;
            }
            return null;
        }

        function formatResult(value) {
            if (value === true) return 'true';
            if (value === false) return 'false';
            if (value === undefined) return 'undefined';
            if (value === null) return 'null';
            return String(value);
        }

        function getResultClass(value) {
            if (value === true) return 'true';
            if (value === false) return 'false';
            return 'undefined';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Browser info
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('documentMode').textContent = formatResult(document.documentMode);
            document.getElementById('compatMode').textContent = document.compatMode || 'Not available';

            // Test results
            var method1Result = method1_MSInputMethodContext();
            var method2Result = method2_TridentDocumentMode();
            var method3Result = method3_DocumentModeOnly();
            var method4Result = method4_MSIEUserAgent();
            var method5Result = method5_ActiveXObject();
            var method6Result = method6_ConditionalCompilation();

            document.getElementById('method1').textContent = formatResult(method1Result);
            document.getElementById('method1').className = 'test-result ' + getResultClass(method1Result);

            document.getElementById('method2').textContent = formatResult(method2Result);
            document.getElementById('method2').className = 'test-result ' + getResultClass(method2Result);

            document.getElementById('method3').textContent = formatResult(method3Result);
            document.getElementById('method3').className = 'test-result ' + getResultClass(method3Result);

            document.getElementById('method4').textContent = formatResult(method4Result);
            document.getElementById('method4').className = 'test-result ' + getResultClass(method4Result);

            document.getElementById('method5').textContent = formatResult(method5Result);
            document.getElementById('method5').className = 'test-result ' + getResultClass(method5Result);

            document.getElementById('method6').textContent = formatResult(method6Result);
            document.getElementById('method6').className = 'test-result ' + getResultClass(method6Result);

            // Final results
            var isIE = isInternetExplorer();
            var ieVersion = getIEVersion();

            document.getElementById('finalResult').textContent = formatResult(isIE);
            document.getElementById('finalResult').className = 'test-result ' + getResultClass(isIE);
            document.getElementById('ieVersion').textContent = ieVersion ? 'IE ' + ieVersion : 'Not IE';
        });
    </script>
</body>
</html>
