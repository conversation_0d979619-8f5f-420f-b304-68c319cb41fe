<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Detection Test - DeTrim</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .browser-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .ie-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .edge-redirect {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .warning-btn {
            background: #ffc107;
            color: #212529;
        }
        .warning-btn:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DeTrim Browser Detection Test</h1>
        
        <div class="browser-info">
            <h3>Current Browser Information:</h3>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
            <p><strong>Browser:</strong> <span id="browserName"></span></p>
            <p><strong>Is Internet Explorer:</strong> <span id="isIE"></span></p>
        </div>

        <div id="ieWarning" class="ie-warning" style="display: none;">
            <h3>⚠️ Internet Explorer Detected</h3>
            <p>For the best experience with DeTrim, we recommend using Microsoft Edge or another modern browser.</p>
        </div>

        <div class="edge-redirect">
            <h3>Test Edge Redirect</h3>
            <p>Click the button below to test the Edge redirect functionality:</p>
            <button onclick="testEdgeRedirect()">Test Edge Redirect</button>
            <button onclick="testEdgeRedirectNoConfirm()" class="warning-btn">Test Edge Redirect (No Confirmation)</button>
        </div>

        <div>
            <h3>Manual Testing</h3>
            <p>To test this functionality:</p>
            <ol>
                <li>Open this page in Internet Explorer</li>
                <li>You should see a confirmation dialog asking if you want to open in Edge</li>
                <li>Click "OK" to test the Edge redirect</li>
                <li>If Edge is installed, it should open with the same URL</li>
            </ol>
        </div>
    </div>

    <script>
        // Browser detection functions (same as in the service)
        function isInternetExplorer() {
            var isIE = /*@cc_on!@*/false || !!document.documentMode;
            var isIE11 = !!window.MSInputMethodContext && !!document.documentMode;
            return isIE || isIE11;
        }

        function getBrowserName() {
            var userAgent = navigator.userAgent;
            
            if (isInternetExplorer()) {
                return 'Internet Explorer';
            } else if (userAgent.indexOf('Edge') > -1) {
                return 'Microsoft Edge';
            } else if (userAgent.indexOf('Chrome') > -1) {
                return 'Google Chrome';
            } else if (userAgent.indexOf('Firefox') > -1) {
                return 'Mozilla Firefox';
            } else if (userAgent.indexOf('Safari') > -1) {
                return 'Safari';
            } else {
                return 'Unknown Browser';
            }
        }

        function redirectToEdge(showConfirmation) {
            var currentUrl = window.location.href;
            var edgeUrl = 'microsoft-edge:' + currentUrl;
            
            if (showConfirmation) {
                var userConfirm = confirm(
                    'Internet Explorer is not supported for optimal performance.\n\n' +
                    'Would you like to open this application in Microsoft Edge for the best experience?\n\n' +
                    'Click OK to open in Edge, or Cancel to continue with Internet Explorer.'
                );
                
                if (!userConfirm) {
                    console.warn('User chose to continue with Internet Explorer.');
                    return;
                }
            }

            try {
                window.location.href = edgeUrl;
                
                setTimeout(function() {
                    alert(
                        'Please copy this URL and paste it into Microsoft Edge:\n\n' + currentUrl +
                        '\n\nFor the best experience, we recommend using Microsoft Edge or another modern browser.'
                    );
                }, 1000);
            } catch (error) {
                alert(
                    'Please copy this URL and paste it into Microsoft Edge:\n\n' + currentUrl +
                    '\n\nFor the best experience, we recommend using Microsoft Edge or another modern browser.'
                );
            }
        }

        function testEdgeRedirect() {
            redirectToEdge(true);
        }

        function testEdgeRedirectNoConfirm() {
            redirectToEdge(false);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('browserName').textContent = getBrowserName();
            document.getElementById('isIE').textContent = isInternetExplorer() ? 'Yes' : 'No';
            
            if (isInternetExplorer()) {
                document.getElementById('ieWarning').style.display = 'block';
            }
        });
    </script>
</body>
</html>
