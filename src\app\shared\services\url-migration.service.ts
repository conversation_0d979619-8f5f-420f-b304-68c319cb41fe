import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class UrlMigrationService {

  constructor(private router: Router) {}

  /**
   * Check if the current URL contains a hash fragment that should be migrated
   * to clean URL format. This helps with backward compatibility.
   */
  public checkAndMigrateHashUrl(): void {
    const currentUrl = window.location.href;
    const hashIndex = currentUrl.indexOf('#/');
    
    if (hashIndex !== -1) {
      // Extract the path after the hash
      const hashPath = currentUrl.substring(hashIndex + 2);
      
      // Navigate to the clean URL
      this.router.navigateByUrl('/' + hashPath, { replaceUrl: true });
    }
  }

  /**
   * Handle legacy hash-based URLs by redirecting to clean URLs
   */
  public handleLegacyUrls(): void {
    // Check if we're coming from a hash-based URL
    if (window.location.hash && window.location.hash.startsWith('#/')) {
      const cleanPath = window.location.hash.substring(2);
      window.history.replaceState(null, '', window.location.pathname + cleanPath);
      this.router.navigateByUrl('/' + cleanPath);
    }
  }
}
