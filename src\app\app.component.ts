import {
  Component,
  ViewContainerRef,
  OnInit,
  ViewChild,
  Inject,
  LOCALE_ID,
} from "@angular/core";

// Moduli esterni
import { TranslateService } from "@ngx-translate/core";

// Servizi
import { MessageService } from "./shared/messages/services/message.service";
import { MenuService } from "./shared/menu/services/menu.service";
import { BrowserDetectionService } from "./shared/services/browser-detection.service";
import { UrlMigrationService } from "./shared/services/url-migration.service";
import { getCurrentAppCode, isZA0 } from "./shared/utils/app-code.util";

// Componenti
import { HeadMenuComponent } from "./shared/menu/components/head-menu/head-menu.component";
import { UserDataService } from "./shared/user-data/user-data.service";
import { Headers, Http, RequestOptions, Response } from "@angular/http";
import { DOCUMENT } from "@angular/platform-browser";

@Component({
  selector: "app-root",
  templateUrl: "./app.component.html",
  styleUrls: ["./app.component.css"],
})
export class AppComponent implements OnInit {
  @ViewChild(HeadMenuComponent) headMenu: HeadMenuComponent;

  constructor(
    private msgService: MessageService,
    vcr: ViewContainerRef,
    private translate: TranslateService,
    public menuService: MenuService,
    @Inject(LOCALE_ID) private locale: string,
    public userDataService: UserDataService,
    private http: Http,
    @Inject(DOCUMENT) private document: any,
    private browserDetectionService: BrowserDetectionService,
    private urlMigrationService: UrlMigrationService
  ) {
    this.msgService.toaster.setRootViewContainerRef(vcr);
  }

  ngOnInit() {
    // Handle legacy hash-based URLs for backward compatibility
    this.urlMigrationService.handleLegacyUrls();

    this.translate.setDefaultLang(this.locale);

    const userData = this.userDataService.getUserData();
    console.log(userData);
    console.log(userData.profile);
    console.log(this.userDataService.getUserData().profile);

    // Log current base href configuration
    console.log('Current app code:', getCurrentAppCode());
    console.log('Is ZA0 app:', isZA0());

    if (userData && userData.profile === "BIOSUG") {
      this.redirectToSilos();
    }
  }

  // Silos redirection test

  async redirectToSilos(): Promise<void> {
    const userData = this.userDataService.getUserData();
    console.log(userData.username);

    // Get current app code from utility function
    const appCode = getCurrentAppCode();
    const browserName = this.browserDetectionService.getBrowserName();
    const isIE = this.browserDetectionService.isInternetExplorer();
    const isChrome = browserName === 'Google Chrome';

    // Check if AppCode is ZA0 and browser is Chrome or IE, then redirect to Edge
    if (appCode === "ZA0" && (isIE || isChrome)) {
      console.log(`Browser detected: ${browserName}. Redirecting to Edge for ZA0 application.`);
      this.browserDetectionService.redirectToEdge(true);
      return;
    }

    try {
      const response = await this.getAuthData(
        userData.username,
        "ZA0",
        userData.branch
      );
      console.log(response);
      if (response && response !== null) {
        this.dataToSilos(response);
      }
    } catch (error) {
      console.log(error.message);
      console.log(error.status);
      console.log(error.statusText);
      console.error("Authentication failed:", error);
      this.msgService.showError("Authentication failed", "Error");
    }
  }

  getAuthData(
    userId: any,
    applicationCode: any,
    branchCode: any
  ): Promise<any> {
    //  const url = `http://localhost:4200/UBZ-ESA-RS/service/userService/v1/users/${userId}/${applicationCode}/${branchCode}`;

    const url = `https://ubz-uj.collaudo.usinet.it/UBZ-ESA-RS/service/userService/v1/users/${userId}/${applicationCode}/${branchCode}`;

    const headers = new Headers({
      Accept: "application/json",
      "Content-Type": "application/json",
      "Cache-Control": "no-cache",
      Pragma: "no-cache",
    });

    const options = new RequestOptions({ headers: headers });

    return this.http
      .get(url, options)
      .map((res: Response) => res.json())
      .toPromise();
  }

  dataToSilos(dataSilos: any): void {
    const url =
      "https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication";

    const headers = new Headers({
      Accept: "application/json",
      "Content-Type": "application/json",
      "Cache-Control": "no-cache",
      Pragma: "no-cache",
    });

    const options = new RequestOptions({
      headers: headers,
      withCredentials: true,
    });

    const postData = JSON.stringify(dataSilos);

    this.http
      .post(url, postData, options)
      .map((res: Response) => res)
      .toPromise()
      .then((res: Response) => {
        console.log("Response status:", res.status);

        if (res.status === 200) {
          this.document.location.replace(res.url || url);
        } else {
          console.error("Authentication failed with status:", res.status);
          this.msgService.showError("Authentication failed", "Error");
        }
      })
      .catch((error) => {
        console.error("Authentication error:", error);
        this.msgService.showError("Authentication failed", "Error");
      });
  }
  // Silos redirection test

  closeSideMenu() {
    this.menuService.sideMenuPropriety = "toggled";
    this.menuService.footerProperty = "";
    this.headMenu.menuIconClass = "menu-icon-switch icon-menu";
    this.headMenu.userMenuClass = "userInfo";
  }
}