import { Injectable, Inject } from '@angular/core';

@Injectable()
export class BaseHrefService {
  private _currentAppCode: string;
  private _baseHref: string;

  constructor() {
    this.initializeBaseHref();
  }

  /**
   * Initialize base href based on current context
   */
  private initializeBaseHref(): void {
    this._currentAppCode = this.determineAppCode();
    this._baseHref = `/UBZ-EFA-PF/${this._currentAppCode}/`;
  }

  /**
   * Determine the app code from various sources
   * @returns string - The determined app code (UBZ or ZA0)
   */
  private determineAppCode(): string {
    // Check URL parameter first
    const urlParams = new URLSearchParams(window.location.search);
    const appCodeFromUrl = urlParams.get('appCode');
    if (appCodeFromUrl) {
      return appCodeFromUrl.toUpperCase();
    }

    // Check if path contains ZA0 indicator
    const currentPath = window.location.pathname;
    if (currentPath.indexOf('/ZA0/') !== -1 || currentPath.indexOf('ZA0') !== -1) {
      return 'ZA0';
    }

    // Check hostname for ZA0 indicator
    const hostname = window.location.hostname;
    if (hostname.toLowerCase().indexOf('za0') !== -1) {
      return 'ZA0';
    }

    // Check current base href
    const baseElement = document.querySelector('base');
    if (baseElement) {
      const currentBaseHref = baseElement.getAttribute('href');
      if (currentBaseHref && currentBaseHref.indexOf('/ZA0/') !== -1) {
        return 'ZA0';
      }
    }

    // Default to UBZ
    return 'UBZ';
  }

  /**
   * Get the current app code
   * @returns string - Current app code
   */
  public getCurrentAppCode(): string {
    return this._currentAppCode;
  }

  /**
   * Get the current base href
   * @returns string - Current base href
   */
  public getBaseHref(): string {
    return this._baseHref;
  }

  /**
   * Set a new app code and update base href
   * @param appCode - New app code (UBZ or ZA0)
   */
  public setAppCode(appCode: string): void {
    this._currentAppCode = appCode.toUpperCase();
    this._baseHref = `/UBZ-EFA-PF/${this._currentAppCode}/`;
    this.updateBaseHrefElement();
  }

  /**
   * Update the base href element in the DOM
   */
  private updateBaseHrefElement(): void {
    const baseElement = document.querySelector('base');
    if (baseElement) {
      baseElement.setAttribute('href', this._baseHref);
      console.log('Base href updated to:', this._baseHref);
    }
  }

  /**
   * Check if current app code is ZA0
   * @returns boolean - True if current app code is ZA0
   */
  public isZA0(): boolean {
    return this._currentAppCode === 'ZA0';
  }

  /**
   * Check if current app code is UBZ
   * @returns boolean - True if current app code is UBZ
   */
  public isUBZ(): boolean {
    return this._currentAppCode === 'UBZ';
  }

  /**
   * Generate a full URL with the current base href
   * @param relativePath - Relative path to append
   * @returns string - Full URL with base href
   */
  public getFullUrl(relativePath: string = ''): string {
    const baseUrl = window.location.origin + this._baseHref;
    return relativePath ? baseUrl + relativePath.replace(/^\//, '') : baseUrl;
  }

  /**
   * Navigate to a different app code context
   * @param appCode - Target app code
   * @param preservePath - Whether to preserve current path
   */
  public navigateToAppCode(appCode: string, preservePath: boolean = true): void {
    const newBaseHref = `/UBZ-EFA-PF/${appCode.toUpperCase()}/`;
    let targetUrl = window.location.origin + newBaseHref;
    
    if (preservePath) {
      // Extract current path relative to base href
      const currentPath = window.location.pathname;
      const currentBaseHref = this._baseHref;
      
      if (currentPath.startsWith(currentBaseHref)) {
        const relativePath = currentPath.substring(currentBaseHref.length);
        targetUrl += relativePath;
      }
      
      // Preserve query parameters and hash
      if (window.location.search) {
        targetUrl += window.location.search;
      }
      if (window.location.hash) {
        targetUrl += window.location.hash;
      }
    }
    
    window.location.href = targetUrl;
  }
}
